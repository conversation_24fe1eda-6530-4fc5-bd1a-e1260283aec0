* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #000 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.nav {
    background: linear-gradient(135deg, #7356b1, #7354ae);;
    width: 100%;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.user {
    display: flex;
    align-items: center;
    gap: 10px;
}

#username {
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.user i {
    color: white;
    font-size: 1.5rem;
    background: rgba(255,255,255,0.2);
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.user i:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.logout {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.logout:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.container {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

.cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.card {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 0;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.cardHead {
    background: linear-gradient(135deg, #4facfe 0%, #71489b 100%);
    color: white;
    padding: 20px;
    font-weight: 600;
    font-size: 1.1rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cardHead::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.card:hover .cardHead::before {
    left: 100%;
}

.cardContent {
    padding: 25px;
    text-align: center;
    font-size: 1.1rem;
    line-height: 1.6;
}

.cardContent .value {
    font-size: 2.5rem;
    font-weight: bold;
    color: black;
    margin: 10px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cardContent .label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.graphs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 30px;
}

.graph-container {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.graph-container:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.graph-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

svg {
    width: 100%;
    height: auto;
}

.line-chart path {
    fill: none;
    stroke-width: 3;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.xp-line {
    stroke: #4facfe;
}

.success-line {
    stroke: #4facfe;
}

.failed-line {
    stroke: #f5576c;
}

.data-point {
    stroke: white;
    stroke-width: 2;
    transition: all 0.3s ease;
}

.xp-point {
    fill: #4facfe;
}

.success-point {
    fill: #4facfe;
}

.failed-point {
    fill: #f5576c;
}

.data-point:hover {
    r: 6;
    transform: scale(1.2);
}

.grid-line {
    stroke: #e0e0e0;
    stroke-width: 1;
}

.axis-text {
    font-family: 'Segoe UI', sans-serif;
    font-size: 12px;
    fill: #666;
}

.legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #666;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

@media (max-width: 768px) {
    .nav {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .container {
        padding: 20px;
    }
    
    .graphs {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card, .graph-container {
    animation: fadeInUp 0.6s ease forwards;
}

.card:nth-child(2) {
    animation-delay: 0.1s;
}

.card:nth-child(3) {
    animation-delay: 0.2s;
}

.graph-container:nth-child(1) {
    animation-delay: 0.3s;
}

.graph-container:nth-child(2) {
    animation-delay: 0.4s;
}



.container-chart {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 100%;
}

.graph-title {
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
}

#pieChart {
    max-width: 350px;
    height: 350px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.legend {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.legend-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.legend-text {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.legend-percentage {
    font-size: 12px;
    color: #666;
    margin-left: auto;
    font-weight: bold;
}

.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.pie-slice {
    transition: all 0.3s ease;
    cursor: pointer;
}

.pie-slice:hover {
    filter: brightness(1.1);
    transform-origin: center;
}

.center-text {
    text-anchor: middle;
    dominant-baseline: middle;
    font-weight: bold;
    fill: #333;
}