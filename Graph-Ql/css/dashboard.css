.dashboard {
    display: flex;
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: 0 8px 20px var(--shadow);
    overflow: hidden;
    width: 70%;
    height: 85vh;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.hidden {
    display: none;
}

.sidebar {
    width: 250px;
    background-color: var(--gray-dark);
    color: var(--white);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.sidebar-nav li i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-nav li:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav li.active {
    background-color: var(--primary);
    color: var(--white);
    font-weight: bold;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

#logout-btn {
    width: 100%;
    padding: 10px;
    background-color: transparent;
    color: var(--white);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

#logout-btn i {
    margin-right: 10px;
}

#logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.dashboard-header {
    padding: 15px 20px;
    background-color: var(--white);
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hamburger-menu {
    display: none;
    font-size: 20px;
    cursor: pointer;
}

.user-info {
    display: flex;
    align-items: center;
}

#user-greeting {
    margin-right: 15px;
    font-weight: 500;
}

.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: var(--gray);
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar i {
    font-size: 20px;
    color: var(--gray-dark);
}

.content-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: var(--gray);
}

.dashboard-section {
    display: none;
    animation: fadeIn 0.5s ease;
}

.dashboard-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dashboard-section h2 {
    margin-bottom: 20px;
    color: var(--text-dark);
    border-bottom: 2px solid var(--primary);
    padding-bottom: 10px;
    display: inline-block;
}

.profile-card {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 4px 10px var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--gray);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.profile-avatar i {
    font-size: 40px;
    color: var(--gray-dark);
}

.profile-info h3 {
    font-size: 24px;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.profile-info p {
    color: var(--text-light);
}

.profile-details {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.detail-card {
    background-color: var(--gray);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px var(--shadow);
}

.detail-card i {
    font-size: 30px;
    color: var(--primary);
    margin-bottom: 10px;
}

.detail-card h4 {
    font-size: 16px;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.detail-card p {
    font-size: 20px;
    font-weight: bold;
    color: var(--primary-dark);
}

.stats-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

#graph-selector {
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ddd;
    background-color: var(--white);
}

.graph-container {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 4px 10px var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
}

#stats-graph .axis path,
#stats-graph .axis line {
    fill: none;
    stroke: #ccc;
    shape-rendering: crispEdges;
}

#stats-graph .axis text {
    font-size: 12px;
    fill: var(--text-light);
}

#stats-graph .bar {
    fill: var(--primary);
    transition: fill 0.3s ease;
}

#stats-graph .bar:hover {
    fill: var(--primary-dark);
}

#stats-graph .line {
    fill: none;
    stroke: var(--primary);
    stroke-width: 2;
}

#stats-graph .dot {
    fill: var(--primary-dark);
    stroke: var(--white);
    stroke-width: 1.5;
}

#stats-graph .area {
    fill: var(--primary-light);
    opacity: 0.3;
}

#stats-graph .grid line {
    stroke: #eee;
}

#stats-graph .grid path {
    stroke-width: 0;
}

#stats-graph .tooltip {
    position: absolute;
    background-color: var(--gray-dark);
    color: var(--white);
    padding: 8px 10px;
    border-radius: 4px;
    pointer-events: none;
    font-size: 12px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

#stats-graph .legend {
    font-size: 12px;
    fill: var(--text-dark);
}

.grades-table-container,
.audits-table-container {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 4px 10px var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
    overflow-x: auto;
}

.grades-table,
.audits-table {
    width: 100%;
    border-collapse: collapse;
}

.grades-table th,
.audits-table th {
    background-color: var(--gray);
    padding: 12px 15px;
    text-align: left;
    font-weight: bold;
    color: var(--text-dark);
    border-bottom: 2px solid #ddd;
}

.grades-table td,
.audits-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    color: var(--text-dark);
}

.grades-table tr:last-child td,
.audits-table tr:last-child td {
    border-bottom: none;
}

.grades-table tr:hover,
.audits-table tr:hover {
    background-color: var(--gray);
}

.loading-row {
    text-align: center;
    color: var(--text-light);
    padding: 30px 0 !important;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

.status-pass {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-fail {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-in-progress {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning);
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.skills-container {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 4px 10px var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
    min-height: 400px;
}

#skills-chart .skill-bar {
    height: 20px;
    margin-bottom: 10px;
    background-color: var(--gray);
    border-radius: 10px;
    overflow: hidden;
}

#skills-chart .skill-progress {
    height: 100%;
    background-color: var(--primary);
    border-radius: 10px 0 0 10px;
    transition: width 1s ease;
}

#skills-chart .skill-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

#skills-chart .skill-name {
    font-weight: bold;
}

#skills-chart .skill-value {
    color: var(--primary-dark);
}

.audits-summary {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}
