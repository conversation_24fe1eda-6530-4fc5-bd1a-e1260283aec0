body {
    background: linear-gradient(120deg, var(--primary-light), var(--primary-dark));
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 100%;
    max-width: 1200px;
    padding: 0 20px;
}

.login-card {
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: 0 8px 20px var(--shadow);
    overflow: hidden;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    padding: 40px;
    transition: transform 0.3s ease;
}

.login-card:hover {
    transform: translateY(-5px);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
}

.login-header h1 {
    color: var(--primary-dark);
    font-size: 28px;
    margin-bottom: 10px;
}

.login-header p {
    color: var(--text-light);
    font-size: 16px;
}

.error-message {
    padding: 0.5rem;
    margin-bottom: 1rem;
    /* margin-left: 1rem; */
    background-color: var(--primary-light);
    color : var(--danger);
    border-radius: 10px;
    font-weight: bold;
}

.input-group {
    position: relative;
    margin-bottom: 20px;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary);
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    cursor: pointer;
}

.toggle-password:hover {
    color: var(--primary);
}

input[type="text"],
input[type="password"] {
    width: 100%;
    padding: 15px 45px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="password"]:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(255, 127, 80, 0.2);
    outline: none;
}

.remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.remember {
    display: flex;
    align-items: center;
}

.remember input[type="checkbox"] {
    margin-right: 8px;
    accent-color: var(--primary);
}

.login-btn {
    width: 100%;
    background-color: var(--primary);
    color: var(--white);
    border: none;
    border-radius: 8px;
    padding: 15px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.login-btn:hover {
    background-color: var(--primary-dark);
}
