<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MovieHub - Discover Your Next Watch</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .tagline {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .search-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .search-container {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 50px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
            min-width: 250px;
        }

        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
        }

        .search-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .filters {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            background: white;
            cursor: pointer;
            outline: none;
        }

        .nav-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .tab-btn {
            padding: 12px 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .tab-btn.active, .tab-btn:hover {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            transform: translateY(-2px);
        }

        .content-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 25px;
            margin-bottom: 20px;
        }

        .content-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .content-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .poster {
            width: 100%;
            height: 300px;
            object-fit: cover;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
        }

        .card-content {
            padding: 15px;
        }

        .card-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            font-size: 1.1rem;
        }

        .card-info {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .rating {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-top: 10px;
        }

        .star {
            color: #ffd700;
        }

        .watchlist-btn {
            width: 100%;
            padding: 10px;
            margin-top: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .add-to-watchlist {
            background: #4CAF50;
            color: white;
        }

        .remove-from-watchlist {
            background: #f44336;
            color: white;
        }

        .watchlist-btn:hover {
            transform: translateY(-2px);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #667eea;
            font-size: 1.2rem;
        }

        .error {
            text-align: center;
            padding: 40px;
            color: #f44336;
            font-size: 1.1rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .page-btn {
            padding: 10px 15px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .page-btn:hover, .page-btn.active {
            background: #667eea;
            color: white;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 800px;
            width: 90%;
            max-height: 90%;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            color: #999;
        }

        .modal-body {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .modal-poster {
            width: 300px;
            height: 450px;
            object-fit: cover;
            border-radius: 15px;
            flex-shrink: 0;
        }

        .modal-info {
            flex: 1;
            min-width: 300px;
        }

        .modal-title {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #333;
        }

        .info-group {
            margin-bottom: 15px;
        }

        .info-label {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .ratings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .rating-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .rating-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .rating-source {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .cast-list {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .cast-member {
            background: #e3f2fd;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            color: #1976d2;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .search-container {
                flex-direction: column;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .filters {
                flex-direction: column;
                align-items: stretch;
            }
            
            .content-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .modal-body {
                flex-direction: column;
            }
            
            .modal-poster {
                width: 100%;
                height: 400px;
            }
        }

        .recommendation-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .recommendation-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .recommendation-reason {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">🎬 MovieHub</div>
            <div class="tagline">Discover Your Next Favorite Movie & TV Show</div>
        </header>

        <div class="search-section">
            <div class="search-container">
                <input type="text" class="search-input" id="searchInput" placeholder="Search for movies and TV shows...">
                <button class="search-btn" id="searchBtn">Search</button>
            </div>
            <div class="filters">
                <select class="filter-select" id="typeFilter">
                    <option value="">All Types</option>
                    <option value="movie">Movies</option>
                    <option value="tv">TV Shows</option>
                </select>
                <select class="filter-select" id="genreFilter">
                    <option value="">All Genres</option>
                </select>
                <select class="filter-select" id="yearFilter">
                    <option value="">All Years</option>
                </select>
            </div>
        </div>

        <div class="nav-tabs">
            <button class="tab-btn active" data-tab="trending">Trending</button>
            <button class="tab-btn" data-tab="search">Search Results</button>
            <button class="tab-btn" data-tab="watchlist">My Watchlist</button>
            <button class="tab-btn" data-tab="recommendations">Recommendations</button>
        </div>

        <div class="content-section" id="trendingSection">
            <h2 class="section-title">🔥 Trending Now</h2>
            <div class="content-grid" id="trendingGrid"></div>
        </div>

        <div class="content-section" id="searchSection" style="display: none;">
            <h2 class="section-title">Search Results</h2>
            <div class="content-grid" id="searchGrid"></div>
            <div class="pagination" id="searchPagination"></div>
        </div>

        <div class="content-section" id="watchlistSection" style="display: none;">
            <h2 class="section-title">📺 My Watchlist</h2>
            <div class="content-grid" id="watchlistGrid"></div>
        </div>

        <div class="content-section" id="recommendationsSection" style="display: none;">
            <h2 class="section-title">🎯 Recommended For You</h2>
            <div id="recommendationsGrid"></div>
        </div>
    </div>

    <!-- Modal for detailed view -->
    <div class="modal" id="detailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle"></h2>
                <button class="close-btn" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <img class="modal-poster" id="modalPoster" alt="Poster">
                <div class="modal-info">
                    <div class="info-group">
                        <div class="info-label">Overview</div>
                        <div id="modalOverview"></div>
                    </div>
                    <div class="info-group">
                        <div class="info-label">Release Date</div>
                        <div id="modalDate"></div>
                    </div>
                    <div class="info-group">
                        <div class="info-label">Genres</div>
                        <div id="modalGenres"></div>
                    </div>
                    <div class="info-group">
                        <div class="info-label">Cast</div>
                        <div class="cast-list" id="modalCast"></div>
                    </div>
                    <div class="ratings-grid" id="modalRatings"></div>
                    <button class="watchlist-btn" id="modalWatchlistBtn"></button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class MovieHub {
            constructor() {
                // API Configuration - Replace with your actual API keys
                this.TMDB_API_KEY = 'your_tmdb_api_key_here';
                this.OMDB_API_KEY = 'your_omdb_api_key_here';
                this.TMDB_BASE_URL = 'https://api.themoviedb.org/3';
                this.OMDB_BASE_URL = 'https://www.omdbapi.com';
                this.IMAGE_BASE_URL = 'https://image.tmdb.org/t/p/w500';
                
                // Cache and state management
                this.cache = new Map();
                this.watchlist = this.loadWatchlist();
                this.currentPage = 1;
                this.currentQuery = '';
                this.debounceTimer = null;
                this.genres = new Map();
                
                this.init();
            }

            async init() {
                this.setupEventListeners();
                await this.loadGenres();
                this.populateYearFilter();
                await this.loadTrending();
                this.renderWatchlist();
                this.generateRecommendations();
            }

            setupEventListeners() {
                // Search functionality
                document.getElementById('searchInput').addEventListener('input', (e) => {
                    clearTimeout(this.debounceTimer);
                    this.debounceTimer = setTimeout(() => {
                        if (e.target.value.trim()) {
                            this.search(e.target.value.trim());
                        }
                    }, 500);
                });

                document.getElementById('searchBtn').addEventListener('click', () => {
                    const query = document.getElementById('searchInput').value.trim();
                    if (query) this.search(query);
                });

                // Tab navigation
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        this.switchTab(btn.dataset.tab);
                    });
                });

                // Modal controls
                document.getElementById('closeModal').addEventListener('click', () => {
                    document.getElementById('detailModal').style.display = 'none';
                });

                window.addEventListener('click', (e) => {
                    if (e.target === document.getElementById('detailModal')) {
                        document.getElementById('detailModal').style.display = 'none';
                    }
                });

                // Filter changes
                ['typeFilter', 'genreFilter', 'yearFilter'].forEach(id => {
                    document.getElementById(id).addEventListener('change', () => {
                        if (this.currentQuery) {
                            this.search(this.currentQuery);
                        }
                    });
                });
            }

            async loadGenres() {
                try {
                    const [movieGenres, tvGenres] = await Promise.all([
                        this.fetchWithCache(`${this.TMDB_BASE_URL}/genre/movie/list?api_key=${this.TMDB_API_KEY}`),
                        this.fetchWithCache(`${this.TMDB_BASE_URL}/genre/tv/list?api_key=${this.TMDB_API_KEY}`)
                    ]);

                    const allGenres = [...movieGenres.genres, ...tvGenres.genres];
                    allGenres.forEach(genre => {
                        this.genres.set(genre.id, genre.name);
                    });

                    // Populate genre filter
                    const genreFilter = document.getElementById('genreFilter');
                    const uniqueGenres = [...new Set(allGenres.map(g => g.name))].sort();
                    uniqueGenres.forEach(genre => {
                        const option = document.createElement('option');
                        option.value = genre.toLowerCase();
                        option.textContent = genre;
                        genreFilter.appendChild(option);
                    });
                } catch (error) {
                    console.error('Error loading genres:', error);
                }
            }

            populateYearFilter() {
                const yearFilter = document.getElementById('yearFilter');
                const currentYear = new Date().getFullYear();
                for (let year = currentYear; year >= 1950; year--) {
                    const option = document.createElement('option');
                    option.value = year;
                    option.textContent = year;
                    yearFilter.appendChild(option);
                }
            }

            async fetchWithCache(url, options = {}) {
                if (this.cache.has(url)) {
                    return this.cache.get(url);
                }

                try {
                    const response = await fetch(url, options);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json();
                    this.cache.set(url, data);
                    return data;
                } catch (error) {
                    console.error('Fetch error:', error);
                    throw error;
                }
            }

            async loadTrending() {
                const grid = document.getElementById('trendingGrid');
                grid.innerHTML = '<div class="loading">Loading trending content...</div>';

                try {
                    const data = await this.fetchWithCache(`${this.TMDB_BASE_URL}/trending/all/week?api_key=${this.TMDB_API_KEY}`);
                    this.renderContent(data.results.slice(0, 20), 'trendingGrid');
                } catch (error) {
                    grid.innerHTML = '<div class="error">Failed to load trending content. Please try again later.</div>';
                }
            }

            async search(query, page = 1) {
                this.currentQuery = query;
                this.currentPage = page;
                
                const grid = document.getElementById('searchGrid');
                const section = document.getElementById('searchSection');
                
                if (page === 1) {
                    grid.innerHTML = '<div class="loading">Searching...</div>';
                    this.switchTab('search');
                }

                try {
                    let url = `${this.TMDB_BASE_URL}/search/multi?api_key=${this.TMDB_API_KEY}&query=${encodeURIComponent(query)}&page=${page}`;
                    
                    // Apply filters
                    const typeFilter = document.getElementById('typeFilter').value;
                    if (typeFilter) {
                        url = `${this.TMDB_BASE_URL}/search/${typeFilter}?api_key=${this.TMDB_API_KEY}&query=${encodeURIComponent(query)}&page=${page}`;
                    }

                    const yearFilter = document.getElementById('yearFilter').value;
                    if (yearFilter) {
                        url += `&year=${yearFilter}`;
                    }

                    const data = await this.fetchWithCache(url);
                    let results = data.results || [];

                    // Apply genre filter client-side
                    const genreFilter = document.getElementById('genreFilter').value;
                    if (genreFilter) {
                        results = results.filter(item => {
                            const genres = item.genre_ids || [];
                            return genres.some(id => 
                                this.genres.get(id)?.toLowerCase() === genreFilter
                            );
                        });
                    }

                    if (page === 1) {
                        this.renderContent(results, 'searchGrid');
                        this.renderPagination(data.total_pages, 'searchPagination');
                    }
                } catch (error) {
                    if (page === 1) {
                        grid.innerHTML = '<div class="error">Search failed. Please try again.</div>';
                    }
                }
            }

            renderContent(items, containerId) {
                const container = document.getElementById(containerId);
                
                if (!items || items.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">🎭</div>
                            <h3>No content found</h3>
                            <p>Try adjusting your search terms or filters.</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = items.map(item => this.createContentCard(item)).join('');
            }

            createContentCard(item) {
                const title = item.title || item.name;
                const date = item.release_date || item.first_air_date;
                const year = date ? new Date(date).getFullYear() : 'N/A';
                const rating = item.vote_average ? item.vote_average.toFixed(1) : 'N/A';
                const poster = item.poster_path 
                    ? `${this.IMAGE_BASE_URL}${item.poster_path}`
                    : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDIwMCAzMDAiIGZpbGw9Im5vbGUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjIwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9IiNlMGUwZTAiLz4KICA8dGV4dCB4PSIxMDAiIHk9IjE1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5ObyBJbWFnZTwvdGV4dD4KPC9zdmc+';
                
                const isInWatchlist = this.watchlist.some(w => w.id === item.id);
                const watchlistBtnClass = isInWatchlist ? 'remove-from-watchlist' : 'add-to-watchlist';
                const watchlistBtnText = isInWatchlist ? 'Remove from Watchlist' : 'Add to Watchlist';

                return `
                    <div class="content-card" onclick="movieHub.showDetails(${item.id}, '${item.media_type || (item.title ? 'movie' : 'tv')}')">
                        <img class="poster" src="${poster}" alt="${title}" loading="lazy">
                        <div class="card-content">
                            <div class="card-title">${title}</div>
                            <div class="card-info">${year} • ${item.media_type === 'tv' || item.first_air_date ? 'TV Show' : 'Movie'}</div>
                            <div class="rating">
                                <span class="star">★</span>
                                <span>${rating}/10</span>
                            </div>
                            <button class="watchlist-btn ${watchlistBtnClass}" 
                                    onclick="event.stopPropagation(); movieHub.toggleWatchlist(${item.id}, '${item.media_type || (item.title ? 'movie' : 'tv')}', '${title.replace(/'/g, "\\'")}', '${poster}', ${item.vote_average || 0})">
                                ${watchlistBtnText}
                            </button>
                        </div>
                    </div>
                `;
            }

            renderPagination(totalPages, containerId) {
                const container = document.getElementById(containerId);
                if (totalPages <= 1) {
                    container.innerHTML = '';
                    return;
                }

                const maxPages = Math.min(totalPages, 10); // Limit to 10 pages for performance
                let pagination = '';

                if (this.currentPage > 1) {
                    pagination += `<button class="page-btn" onclick="movieHub.search('${this.currentQuery}', ${this.currentPage - 1})">Previous</button>`;
                }

                for (let i = 1; i <= maxPages; i++) {
                    const activeClass = i === this.currentPage ? 'active' : '';
                    pagination += `<button class="page-btn ${activeClass}" onclick="movieHub.search('${this.currentQuery}', ${i})">${i}</button>`;
                }

                if (this.currentPage < maxPages) {
                    pagination += `<button class="page-btn" onclick="movieHub.search('${this.currentQuery}', ${this.currentPage + 1})">Next</button>`;
                }

                container.innerHTML = pagination;
            }

            async showDetails(id, type) {
                const modal = document.getElementById('detailModal');
                modal.style.display = 'block';

                try {
                    // Fetch detailed info from TMDB
                    const tmdbData = await this.fetchWithCache(
                        `${this.TMDB_BASE_URL}/${type}/${id}?api_key=${this.TMDB_API_KEY}&append_to_response=credits`
                    );

                    // Fetch additional info from OMDB
                    const title = tmdbData.title || tmdbData.name;
                    const year = tmdbData.release_date || tmdbData.first_air_date;
                    const yearStr = year ? new Date(year).getFullYear() : '';
                    
                    let omdbData = null;
                    try {
                        omdbData = await this.fetchWithCache(
                            `${this.OMDB_BASE_URL}/?apikey=${this.OMDB_API_KEY}&t=${encodeURIComponent(title)}&y=${yearStr}&type=${type === 'tv' ? 'series' : 'movie'}`
                        );
                    } catch (error) {
                        console.warn('OMDB fetch failed:', error);
                    }

                    this.populateModal(tmdbData, omdbData, type);
                } catch (error) {
                    console.error('Error fetching details:', error);
                    document.getElementById('modalTitle').textContent = 'Error Loading Details';
                    document.getElementById('modalOverview').textContent = 'Failed to load content details. Please try again.';
                }
            }

            populateModal(tmdbData, omdbData, type) {
                const title = tmdbData.title || tmdbData.name;
                const poster = tmdbData.poster_path 
                    ? `${this.IMAGE_BASE_URL}${tmdbData.poster_path}`
                    : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQ1MCIgdmlld0JveD0iMCAwIDMwMCA0NTAiIGZpbGw9Im5vbGUiI